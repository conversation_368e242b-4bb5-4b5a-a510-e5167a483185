package com.dhcc.dsp.business.dataquality_new.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.dhcc.avatar.domain.AvatarThreadContext;
import com.dhcc.dsp.business.dataquality.service.DataQualityPolicyTaskStationService;
import com.dhcc.dsp.business.dataquality_new.dto.*;
import com.dhcc.dsp.business.dataquality_new.service.*;
import com.dhcc.dsp.business.dataquality_new.task.service.DataQualityAlarmTaskService;
import com.dhcc.dsp.common.JsonResult;
import com.dhcc.dsp.system.service.impl.DataCacheService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/${api.version}/dataQuality-new")
@RequiredArgsConstructor
public class DataQualityNewController {

    private final DataQualityNewLibraryService dataQualityNewLibraryService;
    private final DataQualityNewPolicyDetailService dataQualityNewPolicyDetailService;
    private final DataQualityNewTaskService dataQualityNewTaskService;
    private final DataQualityNewTaskObjectService dataQualityNewTaskObjectService;
    private final DataQualityNewStationAuditParamService dataQualityNewStationAuditParamService;
    private final DataQualityNewAuditParamService dataQualityNewAuditParamService;
    private final DataQualityNewTaskParamService dataQualityNewTaskParamService;
    private final DataQualityNewTaskStationService dataQualityNewTaskStationService;
    private final DataQualityPolicyTaskStationService dataQualityPolicyTaskStationService;
    private final DataQualityAuditParamTaskService dataQualityAuditParamTaskService;
    private final DataQualityAuditExcludeService dataQualityAuditExcludeService;
    private final DataQualityAlarmTaskService dataQualityAlarmTaskService;

    @Resource
    private DataCacheService dataCacheService;

    // 缓存过期时间常量 - getTaskList接口缓存10分钟
    private static final int TASK_LIST_CACHE_EXPIRE_TIME = 600;



    /**
     * 设置缓存数据
     */
    private void setCacheData(String methodName, Object data, Object... params) {
        if (data == null) {
            return;
        }
        String cacheKey = generateCacheKey(methodName, params);
        Map<String, Object> cacheData = new HashMap<>();
        cacheData.put(cacheKey, data);
        dataCacheService.putObject("data_quality_new_" + methodName, cacheData, TASK_LIST_CACHE_EXPIRE_TIME);
    }

    /**
     * 从缓存获取数据
     */
    private Object getFromCache(String methodName, Object... params) {
        String cacheKey = generateCacheKey(methodName, params);
        Map<String, Object> cacheData = dataCacheService.fetchMapValues("data_quality_new_" + methodName, CollUtil.newArrayList(cacheKey));
        if (MapUtil.isNotEmpty(cacheData)) {
            return cacheData.get(cacheKey);
        }
        return null;
    }

    /**
     * 生成缓存key，确保包含用户信息和所有查询条件
     */
    private String generateCacheKey(String methodName, Object... params) {
        StringBuilder keyBuilder = new StringBuilder();

        // 添加方法名
        keyBuilder.append(methodName).append("_");

        // 添加用户信息，确保不同用户的缓存隔离
        String userName = AvatarThreadContext.userName.get();
        keyBuilder.append("user_").append(userName != null ? userName : "anonymous").append("_");

        // 添加查询参数的详细信息
        for (int i = 0; i < params.length; i++) {
            Object param = params[i];
            if (param != null) {
                // 使用对象的toString方法，包含所有字段信息
                keyBuilder.append("param").append(i).append("_").append(param.toString().hashCode()).append("_");
            }
        }

        return keyBuilder.toString();
    }

    @PostMapping("/downloadTaskStationTemplate")
    public void downloadTaskStationTemplate(HttpServletResponse response) {
        dataQualityPolicyTaskStationService.downloadTemplate(response);
    }

    @PostMapping("/getLibraryTree")
    public JsonResult getLibraryTree(String id, Integer auditObjectType) {
        return JsonResult.ok().put("data", dataQualityNewLibraryService.getLibraryTree(id, auditObjectType));
    }

    @PostMapping("/saveLibrary")
    public JsonResult saveLibrary(@RequestBody DataQualityLibraryDTO dataQualityLibraryDTO) {
        dataQualityNewLibraryService.saveLibrary(dataQualityLibraryDTO);
        return JsonResult.ok();
    }

    @PostMapping("/getPolicyDetail")
    public JsonResult getPolicyDetail(String libraryId) {
        return JsonResult.ok().put("data", dataQualityNewPolicyDetailService.getDetailByLibraryId(libraryId));
    }

    @PostMapping("/removeLibrary")
    public JsonResult removeLibrary(String id) {
        dataQualityNewLibraryService.removeLibrary(id);
        return JsonResult.ok();
    }

    @PostMapping("/saveTask")
    public JsonResult saveTask(@RequestBody DataQualityTaskDTO dataQualityTaskDTO) {
        dataQualityNewTaskService.saveTask(dataQualityTaskDTO);
        return JsonResult.ok();
    }

    @PostMapping("saveFlowTaskRelate")
    public JsonResult saveFlowTaskRelate(@RequestBody FlowTaskRelateDTO flowTaskRelateDTO) {
        dataQualityNewTaskService.saveFlowTaskRelate(flowTaskRelateDTO);
        return JsonResult.ok();
    }

    @PostMapping("/getTaskParamDetail")
    public JsonResult getTaskParamDetail(String taskId) {
        return JsonResult.ok().put("data", dataQualityNewTaskParamService.getTaskParamDetail(taskId));
    }

    @PostMapping("/getTaskList")
    public JsonResult getTaskList(@RequestBody QueryTaskDTO queryTaskDTO) {
        // 先从缓存获取
        @SuppressWarnings("unchecked")
        Map<String, Object> cachedResult = (Map<String, Object>) getFromCache("getTaskList", queryTaskDTO);
        if (cachedResult != null) {
            return JsonResult.ok().put("data", cachedResult);
        }

        // 缓存未命中，调用服务获取数据
        Map<String, Object> result = dataQualityNewTaskService.getTaskList(queryTaskDTO);

        // 将结果存入缓存
        setCacheData("getTaskList", result, queryTaskDTO);

        return JsonResult.ok().put("data", result);
    }

    @PostMapping("/enableTask")
    public JsonResult enableTask(String id, boolean enable) {
        dataQualityNewTaskService.enableTask(id, enable);
        return JsonResult.ok();
    }

    @PostMapping("/removeTask")
    public JsonResult removeTask(String id) {
        dataQualityNewTaskService.removeTask(id);
        return JsonResult.ok();
    }

    @PostMapping("/getTaskAuditObject")
    public JsonResult getTaskAuditObject(String taskId) {
        return JsonResult.ok().put("data", dataQualityNewTaskObjectService.getTaskAuditObject(taskId));
    }

    @PostMapping("/updateTaskAuditObject")
    public JsonResult updateTaskAuditObject(@RequestBody DataQualityTaskDTO dataQualityTaskDTO) {
        dataQualityNewTaskService.updateTaskAuditObject(dataQualityTaskDTO);
        return JsonResult.ok();
    }

    @PostMapping("/getStationAuditDetail")
    public JsonResult getStationAuditDetail(@RequestBody QueryStationParamDTO queryStationParamDTO) {
        return JsonResult.ok().put("data", dataQualityNewStationAuditParamService.getStationAuditDetail(queryStationParamDTO));
    }

    @PostMapping("/getMissingParamCount")
    public JsonResult getMissingParamCount(@RequestBody QueryStationParamDTO queryStationParamDTO) {
        return JsonResult.ok().put("data", dataQualityNewStationAuditParamService.getMissingParamCount(queryStationParamDTO));
    }

    @PostMapping("/saveAuditParamValueBatch")
    public JsonResult saveAuditParamValueBatch(@RequestBody SaveAuditParamValueDTO saveAuditParamValueDTO) {
        dataQualityNewStationAuditParamService.saveAuditParamValueBatch(saveAuditParamValueDTO);
        return JsonResult.ok();
    }

    @PostMapping("/saveAuditParamValueByCnName")
    public JsonResult saveAuditParamValueByCnName(@RequestBody List<SaveAuditParamByCnNameDTO> saveList) {
        dataQualityNewStationAuditParamService.saveAuditParamValueByCnName(saveList);
        return JsonResult.ok();
    }

    @PostMapping("/getAuditParamList")
    public JsonResult getAuditParamList() {
        return JsonResult.ok().put("data", dataQualityNewAuditParamService.getAuditParamList());
    }

    @PostMapping("/getStationListByObject")
    public JsonResult getStationListByObject(String objectId) {
        return JsonResult.ok().put("data", dataQualityNewTaskStationService.getStationListByObject(objectId));
    }

    @PostMapping("/viewImportData")
    public JsonResult viewImportData(@RequestBody ViewImportDataDTO viewImportDataDTO) {
        return JsonResult.ok().put("data", dataQualityNewTaskStationService.viewImportData(viewImportDataDTO));
    }

    @PostMapping("/getStationAuditParam")
    public JsonResult getStationAuditParam(String stationNumber) {
        return JsonResult.ok().put("data", dataQualityNewStationAuditParamService.getStationAuditParam(stationNumber));
    }

    @PostMapping("/auditRangeCheck")
    public JsonResult auditRangeCheck(@RequestBody AuditRangeCheckDTO auditRangeCheckDTO) {
        return JsonResult.ok().put("data", dataQualityNewTaskStationService.auditRangeCheck(auditRangeCheckDTO));
    }

    @PostMapping("/createAuditParamTask")
    public JsonResult createAuditParamTask(@RequestBody CreateAuditParamTaskDTO createAuditParamTaskDTO) {
        dataQualityAuditParamTaskService.createTask(createAuditParamTaskDTO);
        return JsonResult.ok();
    }

    @PostMapping("/submitAuditParamTask")
    public JsonResult submitAuditParamTask(@RequestBody List<String> taskIdList) {
        dataQualityAuditParamTaskService.submitAuditParamTask(taskIdList);
        return JsonResult.ok();
    }

    @PostMapping("/getAuditParamTaskList")
    public JsonResult getAuditParamTaskList(@RequestBody QueryAuditParamTaskDTO queryAuditParamTaskDTO) {
        return JsonResult.ok().put("data", dataQualityAuditParamTaskService.getAuditParamTaskList(queryAuditParamTaskDTO));
    }

    @PostMapping("/approveAuditParamTask")
    public JsonResult approveAuditParamTask(@RequestBody List<String> taskIdList) {
        dataQualityAuditParamTaskService.approveAuditParamTask(taskIdList);
        return JsonResult.ok();
    }

    @PostMapping("/rejectAuditParamTask")
    public JsonResult rejectAuditParamTask(@RequestBody RejectAuditParamTaskDTO dto) {
        dataQualityAuditParamTaskService.rejectAuditParamTask(dto);
        return JsonResult.ok();
    }

    @PostMapping("/saveParam")
    public JsonResult saveParam(@RequestBody List<SaveParamDTO> saveParamDTO) {
        dataQualityNewStationAuditParamService.saveParam(saveParamDTO);
        return JsonResult.ok();
    }

    @PostMapping("/getTimeSeriesLibrary")
    public JsonResult getTimeSeriesLibrary() {
        return JsonResult.ok().put("data", dataQualityNewLibraryService.getTimeSeriesLibrary());
    }

    @PostMapping("/excludeAuditStation")
    public JsonResult excludeAuditStation(@RequestBody List<ExcludeAuditStationDTO> excludeList) {
        dataQualityAuditExcludeService.excludeAuditStation(excludeList);
        return JsonResult.ok();
    }

    @PostMapping("/includeAuditStation")
    public JsonResult includeAuditStation(@RequestBody List<ExcludeAuditStationDTO> excludeList) {
        dataQualityAuditExcludeService.includeAuditStation(excludeList);
        return JsonResult.ok();
    }

    /**
     * 手动触发检测测点数据补值并发送消息
     * @param days 检测最近多少天的数据，默认1天
     * @return 检测结果
     */
    @GetMapping("/checkDataSupplement")
    public Map<String, Object> checkDataSupplement(@RequestParam(required = false) Integer days) {
        return JsonResult.ok().put("data", dataQualityAlarmTaskService.manualCheckStationDataSupplement(days));
    }

    @PostMapping("/exportAuditParam")
    public void exportAuditParam(@RequestBody ExportAuditParamDTO exportAuditParamDTO, HttpServletResponse response) throws IOException {
        dataQualityNewStationAuditParamService.exportAuditParam(exportAuditParamDTO, response);
    }

    @PostMapping("/getSiteListForExport")
    public JsonResult getSiteListForExport() {
        return JsonResult.ok().put("data", dataQualityNewStationAuditParamService.getSiteListForExport());
    }
}
