package com.dhcc.dsp.business.dataquality_new.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.dhcc.avatar.resource.ResourceType;
import com.dhcc.dsp.business.dataquality_new.dao.QualityNewAnalysisMapper;
import com.dhcc.dsp.business.dataquality_new.dto.QualityAnalysisQueryDTO;
import com.dhcc.dsp.business.dataquality_new.enmus.QualityAnalysisEnum;
import com.dhcc.dsp.business.dataquality_new.service.QualityNewAnalysisService;
import com.dhcc.dsp.business.dataquality_new.vo.DataQualityTaskVO;
import com.dhcc.dsp.business.dataquality_new.vo.QualityAnalysisVO;
import com.dhcc.dsp.business.service.DataWarehouseBaseService;
import com.dhcc.dsp.common.consts.ResourceConsts;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class QualityNewAnalysisServiceImpl implements QualityNewAnalysisService {

    private final DataWarehouseBaseService dataWarehouseBaseService;
    private final QualityNewAnalysisMapper qualityAnalysisMapper;

    /**
     * 获取异常率趋势数据
     *
     * @param query 查询条件对象，包含分析类型、时间范围等条件
     * @return 返回异常率趋势的数据列表，列表中每个元素包含站点ID、站点名称、时间、异常总数和异常比率
     */
    @Override
    public List<QualityAnalysisVO> getAbnormalRateTrend(QualityAnalysisQueryDTO query) {

        // 必传参数校验
        checkNecessaryParameter(query);
        // 测点、设备分析目标查询
        Assert.isTrue((QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType()) ||
                QualityAnalysisEnum.ObjectTypeEnum.EQUIPMENT.getCode().equals(query.getObjectType())), "分析目标参数类型错误");

        // 稽核对象数量信息
        List<DataQualityTaskVO> taskVOList = new ArrayList<>();

        // 统计sql主体
        StringBuilder sql = null;
        // 测点分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType())) {

            // 测点稽核数量详情
            if (StrUtil.equals(ResourceConsts.DB_TYPE, ResourceType.ORACLE.getText())) {
                taskVOList = qualityAnalysisMapper.getAuditScopeByPointDetailDameng(query);
            } else {
                taskVOList = qualityAnalysisMapper.getAuditScopeByPointDetail(query);
            }

            sql = new StringBuilder("SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " TO_CHAR( t1.start_time, 'YYYY/MM/DD' ) AS startTime,\n" +
                    " COUNT ( DISTINCT ( t1.object_id ) ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN bcs_standard_station t2 ON t1.object_id = t2.station_number \n" +
                    " AND t2.station_source != '关键测点'\n" +
                    " LEFT JOIN bcs_sites t3 ON t2.site_id = t3.id WHERE 1=1 ");
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        // 设备分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.EQUIPMENT.getCode().equals(query.getObjectType())) {
            // 设备稽核数量详情
            taskVOList = qualityAnalysisMapper.getAuditScopeByDeviceDetail(query);

            sql = new StringBuilder(" SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " TO_CHAR( t1.start_time, 'YYYY/MM/DD' ) AS startTime,\n" +
                    " COUNT ( DISTINCT ( t1.object_id ) ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN dop_device t2 ON SPLIT_PART( t1.object_id, ',', 1 ) = t2.site_id \n" +
                    " AND SPLIT_PART( t1.object_id, ',', 2 ) = t2.code\n" +
                    " LEFT JOIN bcs_sites t3 ON SPLIT_PART( t1.object_id, ',', 1 ) = t3.ID WHERE 1=1 ");
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        sql.append(StrUtil.format(" GROUP BY t3.ID, t3.site_name, startTime ORDER BY t3.site_name, startTime"));
        // 获取异常统计数据
        List<QualityAnalysisVO> qualityAnalysisVOList = dataWarehouseBaseService.queryWithBean(null, QualityAnalysisVO.class, StrUtil.format(sql));

        // 设置异常比率
        for (QualityAnalysisVO a : qualityAnalysisVOList) {
            // 设置默认比率
            a.setRatio("0%");
            // 分子分母不为0则计算比率
            for (DataQualityTaskVO dataQualityTaskVO : taskVOList) {
                if (dataQualityTaskVO.getSiteId().equals(a.getSiteId())) {
                    // 分子分母不为0则计算比率
                    if (null != a.getTotal() && a.getTotal() > 0 && dataQualityTaskVO.getAuditScope() > 0) {
                        BigDecimal divide = new BigDecimal(a.getTotal()).divide(new BigDecimal(dataQualityTaskVO.getAuditScope()), 2, RoundingMode.HALF_UP);
                        a.setRatio(divide.multiply(BigDecimal.valueOf(100)).toString().concat("%"));
                        a.setAuditRange(dataQualityTaskVO.getAuditScope());
                        break;
                    }
                }
            }
        }

        // 在返回结果前添加过滤
        return qualityAnalysisVOList.stream()
            .filter(vo -> vo.getSiteId() != null && vo.getSiteName() != null)
            .collect(Collectors.toList());
    }

    /**
     * 获取异常分析目标数量趋势数据
     *
     * @param query 查询条件对象，包含分析目标类型、时间范围等条件
     * @return 返回异常点数量趋势分析数据列表，列表中每一项包含站点ID、站点名称、分析日期和异常点总数
     */
    @Override
    public List<QualityAnalysisVO> getAbnormalAnalysisTypeCountTrend(QualityAnalysisQueryDTO query) {

        // 必传参数校验
        checkNecessaryParameter(query);
        // 测点、设备分析目标查询
        Assert.isTrue((QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType()) ||
                QualityAnalysisEnum.ObjectTypeEnum.EQUIPMENT.getCode().equals(query.getObjectType())), "分析目标参数类型错误");

        // 统计sql主体
        StringBuilder sql = null;
        // 测点分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder("SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " TO_CHAR( t1.start_time, 'YYYY-MM-DD' ) AS startTime,\n" +
                    " COUNT(1) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN bcs_standard_station t2 ON t1.object_id = t2.station_number \n" +
                    " AND t2.station_source != '关键测点'\n" +
                    " LEFT JOIN bcs_sites t3 ON t2.site_id = t3.ID WHERE 1=1 ");
            
            // 设置固定日期格式查询条件
            if (StrUtil.isNotBlank(query.getSearchStartTime()) && StrUtil.isNotBlank(query.getSearchEndTime())) {
                sql.append(StrUtil.format(" AND ( TO_CHAR( t1.start_time, 'YYYY-MM-DD' ) BETWEEN '{}' AND '{}' )", 
                    query.getSearchStartTime(), query.getSearchEndTime()));
                query.setSearchStartTime(null).setSearchEndTime(null);
            }
            
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        // 设备分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.EQUIPMENT.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " TO_CHAR( t1.start_time, 'YYYY/MM/DD' ) AS startTime,\n" +
                    " COUNT ( DISTINCT ( t1.object_id ) ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN dop_device t2 ON SPLIT_PART( t1.object_id, ',', 1 ) = t2.site_id \n" +
                    " AND SPLIT_PART( t1.object_id, ',', 2 ) = t2.code\n" +
                    " LEFT JOIN bcs_sites t3 ON SPLIT_PART( t1.object_id, ',', 1 ) = t3.ID WHERE 1=1 ");
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        sql.append(" GROUP BY t3.ID, t3.site_name, TO_CHAR(t1.start_time, 'YYYY-MM-DD')")
           .append(" ORDER BY t3.site_name, TO_CHAR(t1.start_time, 'YYYY-MM-DD')");

        // 获取异常分析目标数量
        List<QualityAnalysisVO> qualityAnalysisVOList = dataWarehouseBaseService.queryWithBean(null, QualityAnalysisVO.class, StrUtil.format(sql));

        // 在返回结果前添加过滤
        return qualityAnalysisVOList;
    }

    /**
     * 获取异常数量趋势数据
     *
     * @param query 查询条件对象，包含需要的筛选条件
     * @return 返回异常数量趋势信息的列表，每个趋势信息包含站点ID、站点名称、开始时间及异常数量
     */
    @Override
    public List<QualityAnalysisVO> getAbnormalCountTrend(QualityAnalysisQueryDTO query) {

        // 必传参数校验
        checkNecessaryParameter(query);

        // 统计sql主体
        StringBuilder sql = null;
        // 测点分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder("SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " TO_CHAR( t1.start_time, 'YYYY/MM/DD' ) AS startTime,\n" +
                    " COUNT ( 1 ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN bcs_standard_station t2 ON t1.object_id = t2.station_number \n" +
                    " AND t2.station_source != '关键测点'\n" +
                    " LEFT JOIN bcs_sites t3 ON t2.site_id = t3.ID WHERE 1=1 ");
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        // 设备分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.EQUIPMENT.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " TO_CHAR( t1.start_time, 'YYYY/MM/DD' ) AS startTime,\n" +
                    " COUNT ( 1 ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN dop_device t2 ON SPLIT_PART( t1.object_id, ',', 1 ) = t2.site_id \n" +
                    " AND SPLIT_PART( t1.object_id, ',', 2 ) = t2.code\n" +
                    " LEFT JOIN bcs_sites t3 ON SPLIT_PART( t1.object_id, ',', 1 ) = t3.ID WHERE 1=1 ");
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        // 系统分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.SYSTEM.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " TO_CHAR( t1.start_time, 'YYYY/MM/DD' ) AS startTime,\n" +
                    " COUNT ( 1 ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN bcs_sites t3 ON SPLIT_PART( t1.object_id, ',', 1 ) = t3.ID WHERE 1=1 ");
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        // 厂站分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.STATION.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " TO_CHAR( t1.start_time, 'YYYY/MM/DD' ) AS startTime,\n" +
                    " COUNT ( 1 ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN bcs_sites t3 ON t1.object_id = t3.ID WHERE 1=1 ");
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        sql.append(StrUtil.format(" GROUP BY t3.ID, t3.site_name, startTime ORDER BY t3.site_name, startTime "));

        // 获取异常数量
        List<QualityAnalysisVO> qualityAnalysisVOList = dataWarehouseBaseService.queryWithBean(null, QualityAnalysisVO.class, StrUtil.format(sql));

        // 在返回结果前添加过滤
        return qualityAnalysisVOList.stream()
            .filter(vo -> vo.getSiteId() != null && vo.getSiteName() != null)
            .collect(Collectors.toList());
    }

    /**
     * 获取异常分类数量趋势数据
     *
     * @param query 查询条件对象，包含需要的筛选条件和时间范围等
     * @return 返回异常类型数量趋势的数据列表，列表中每个条目包含异常类型、开始时间和数量
     */
    @Override
    public List<QualityAnalysisVO> getAbnormalTypeCountTrend(QualityAnalysisQueryDTO query) {

        // 必传参数校验
        checkNecessaryParameter(query);

        // 统计sql主体
        StringBuilder sql = null;
        // 测点分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder("SELECT\n" +
                    " t1.exception_type AS exceptionType,\n" +
                    " TO_CHAR( t1.start_time, 'YYYY/MM/DD' ) AS startTime,\n" +
                    " COUNT ( 1 ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN bcs_standard_station t2 ON t1.object_id = t2.station_number \n" +
                    " AND t2.station_source != '关键测点'\n" +
                    " LEFT JOIN bcs_sites t3 ON t2.site_id = t3.ID WHERE 1=1 ");
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        // 设备分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.EQUIPMENT.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder("SELECT\n" +
                    " t1.exception_type AS exceptionType,\n" +
                    " TO_CHAR( t1.start_time, 'YYYY/MM/DD' ) AS startTime,\n" +
                    " COUNT ( 1 ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN dop_device t2 ON SPLIT_PART( t1.object_id, ',', 1 ) = t2.site_id \n" +
                    " AND SPLIT_PART( t1.object_id, ',', 2 ) = t2.code\n" +
                    " LEFT JOIN bcs_sites t3 ON SPLIT_PART( t1.object_id, ',', 1 ) = t3.ID WHERE 1=1 ");
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        // 系统分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.SYSTEM.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t1.exception_type AS exceptionType,\n" +
                    " TO_CHAR( t1.start_time, 'YYYY/MM/DD' ) AS startTime,\n" +
                    " COUNT ( 1 ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN bcs_sites t3 ON SPLIT_PART( t1.object_id, ',', 1 ) = t3.ID WHERE 1=1 ");
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        // 厂站分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.STATION.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t1.exception_type AS exceptionType,\n" +
                    " TO_CHAR( t1.start_time, 'YYYY/MM/DD' ) AS startTime,\n" +
                    " COUNT ( 1 ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN bcs_sites t3 ON t1.object_id = t3.ID WHERE 1=1 ");
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        sql.append(StrUtil.format(" GROUP BY t1.exception_type, startTime ORDER BY t1.exception_type, startTime"));

        // 获取异常分类数量
        List<QualityAnalysisVO> qualityAnalysisVOList = dataWarehouseBaseService.queryWithBean(null, QualityAnalysisVO.class, StrUtil.format(sql));
        return qualityAnalysisVOList;
    }

    /**
     * 获取按分类异常测点比例、按分类异常测点数量、测点分类异常趋势数据
     *
     * @param query 查询条件对象，包含必要的查询参数。
     * @return 返回质量分析视图对象列表，根据查询条件和数据显示折线图或柱状图。
     */
    @Override
    public List<QualityAnalysisVO> getClassifyExceptionRatio(QualityAnalysisQueryDTO query) {

        // 必传参数校验
        checkNecessaryParameter(query);
        // 测点分析目标查询
        Assert.isTrue(QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType()), "分析目标参数类型错误");

        // 统计sql主体
        StringBuilder sql = null;

        // 如果选择了测点类型，返回按日统计折线图（测点分类异常趋势），否则出柱状图
        if (StrUtil.isBlank(query.getPointType())) {
            sql = new StringBuilder(" SELECT \n" +
                    " CASE \n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CY' THEN '振摆'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CT' THEN '温度'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CS' THEN '速率'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CP' THEN '压力'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CM' THEN '湿度'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CL' THEN '液位'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CK' THEN '时间'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CG' THEN '位置'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CF' THEN '流量'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CE' THEN '电气变量'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CD' THEN '密度'\n" +
                    " ELSE '其他' \n" +
                    " END AS pointTypeName,\n" +
                    " COUNT ( DISTINCT ( t1.object_id ) ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN bcs_standard_station t2 ON t1.object_id = t2.station_number \n" +
                    " AND t2.station_source != '关键测点'\n" +
                    " LEFT JOIN bcs_sites t3 ON t2.site_id = t3.ID WHERE 1=1 ");
            // 设置查询条件
            setQueryCriteria(sql, query);
            sql.append(StrUtil.format(" GROUP BY pointTypeName ORDER BY pointTypeName DESC "));
        } else {
            sql = new StringBuilder(" SELECT \n" +
                    " CASE \n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CY' THEN '振摆'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CT' THEN '温度'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CS' THEN '速率'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CP' THEN '压力'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CM' THEN '湿度'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CL' THEN '液位'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CK' THEN '时间'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CG' THEN '位置'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CF' THEN '流量'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CE' THEN '电气变量'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CD' THEN '密度'\n" +
                    " ELSE '其他' \n" +
                    " END AS pointTypeName,\n" +
                    " TO_CHAR( t1.start_time, 'YYYY/MM/DD' ) AS startTime,\n" +
                    " COUNT ( DISTINCT ( t1.object_id ) ) AS total\n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN bcs_standard_station t2 ON t1.object_id = t2.station_number \n" +
                    " AND t2.station_source != '关键测点'\n" +
                    " LEFT JOIN bcs_sites t3 ON t2.site_id = t3.ID WHERE 1=1 ");
            // 设置查询条件
            setQueryCriteria(sql, query);
            sql.append(StrUtil.format(" GROUP BY SUBSTRING ( t1.object_id, 11, 2 ), startTime ORDER BY startTime "));
        }

        // 获取分类测点数量
        List<QualityAnalysisVO> qualityAnalysisVOList = dataWarehouseBaseService.queryWithBean(null, QualityAnalysisVO.class, StrUtil.format(sql));

        // 测点稽核信息
        List<DataQualityTaskVO> taskVOList = qualityAnalysisMapper.getAuditScopeByPointTypeDetail(query);
        // 不分电站
        Map<String, Integer> map = taskVOList.stream().collect(Collectors.groupingBy(a -> a.getPointTypeName(), Collectors.summingInt(a -> a.getAuditScope())));

        // 设置异常比率
        qualityAnalysisVOList.forEach(a -> {
            // 设置默认稽核数量
            a.setAuditRange(0);
            // 设置默认比例
            a.setRatio("0.0%");
            for (String s : map.keySet()) {
                if (a.getPointTypeName().equals(s)) {
                    a.setAuditRange(map.get(s));
                    // 分子分母不为0则计算比率
                    if (Objects.nonNull(a.getTotal()) && a.getTotal() > 0 && map.get(s) > 0) {
                        BigDecimal divide = new BigDecimal(a.getTotal()).divide(new BigDecimal(map.get(s)), 2, RoundingMode.HALF_UP);
                        a.setRatio(divide.multiply(BigDecimal.valueOf(100)).toString().concat("%"));
                        break;
                    }
                }
            }
        });

        return qualityAnalysisVOList;
    }

    /**
     * 获取异常按测点类型分布数据
     *
     * @param query 查询条件，包含需要的参数如测点类型、异常类型等
     * @return 返回异常点类型分布的数据列表，列表中每一项包含异常类型、测点类型、数量等信息
     */
    @Override
    public List<QualityAnalysisVO> getExceptionPointTypeDistribution(QualityAnalysisQueryDTO query) {

        // 必传参数校验
        checkNecessaryParameter(query);
        // 测点分析目标查询
        Assert.isTrue(QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType()), "分析目标参数类型错误");

        // 查询主体sql
        StringBuilder sql = null;

        // 如果选择了测点类型和异常类型，出按日的折线图（测点分类+异常异常趋势），否则出选择维度对应另一维度柱状图
        if (StrUtil.isNotBlank(query.getPointType()) && StrUtil.isNotBlank(query.getExceptionType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " TO_CHAR( t1.start_time, 'YYYY/MM/DD' ) AS startTime,\n" +
                    " COUNT ( 1 ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN bcs_standard_station t2 ON t1.object_id = t2.station_number \n" +
                    " AND t2.station_source != '关键测点'\n" +
                    " LEFT JOIN bcs_sites t3 ON t2.site_id = t3.ID WHERE 1=1 ");
            // 设置查询条件
            setQueryCriteria(sql, query);
            sql.append(StrUtil.format(" GROUP BY startTime ORDER BY startTime "));
        } else {
            sql = new StringBuilder("SELECT\n" +
                    " t1.exception_type AS exceptionType,\n" +
                    " CASE \n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CY' THEN '振摆'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CT' THEN '温度'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CS' THEN '速率'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CP' THEN '压力'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CM' THEN '湿度'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CL' THEN '液位'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CK' THEN '时间'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CG' THEN '位置'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CF' THEN '流量'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CE' THEN '电气变量'\n" +
                    " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CD' THEN '密度'\n" +
                    " ELSE '其他' \n" +
                    " END AS pointTypeName," +
                    " COUNT ( 1 ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN bcs_standard_station t2 ON t1.object_id = t2.station_number \n" +
                    " AND t2.station_source != '关键测点'\n" +
                    " LEFT JOIN bcs_sites t3 ON t2.site_id = t3.ID WHERE 1=1 ");
            // 设置查询条件
            setQueryCriteria(sql, query);
            sql.append(StrUtil.format(" GROUP BY t1.exception_type, pointTypeName ORDER BY t1.exception_type, pointTypeName DESC "));
        }

        // 获取异常测点类型数量
        List<QualityAnalysisVO> qualityAnalysisVOList = dataWarehouseBaseService.queryWithBean(null, QualityAnalysisVO.class, StrUtil.format(sql));
        return qualityAnalysisVOList;
    }

    /**
     * 根据分析类型查询统计数据
     *
     * @param query 查询条件对象，包含分析类型、时间范围等条件
     * @return 分析类型统计数据列表，包含站点ID、站点名称、测点类型（或设备类型）及异常总数
     */
    @Override
    public List<DataQualityTaskVO> getAuditTypeStatistics(QualityAnalysisQueryDTO query) {

        // 必传参数校验
        checkNecessaryParameter(query);

        // 系统分析目标不展示接口数据
        Assert.isTrue((QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType()) ||
                QualityAnalysisEnum.ObjectTypeEnum.EQUIPMENT.getCode().equals(query.getObjectType())), "分析目标参数类型错误");

        // 接口返回数据
        List<DataQualityTaskVO> auditPointsCounts = new ArrayList<>();
        // 测点分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType())) {
            auditPointsCounts = qualityAnalysisMapper.getAuditScopeByPointTypeDetail(query);
        }
        // 设备分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.EQUIPMENT.getCode().equals(query.getObjectType())) {
            auditPointsCounts = qualityAnalysisMapper.getAuditScopeByStationAndDeviceDetail(query);
        }
        return auditPointsCounts;
    }

    /**
     * 获取异常分类统计信息
     *
     * @param query 查询条件对象，包含必要的查询参数
     * @return 返回异常类型统计结果列表，每个结果包含站点ID、站点名称、异常类型以及该异常类型的总数
     */
    @Override
    public List<QualityAnalysisVO> getExceptionTypeStatistics(QualityAnalysisQueryDTO query) {

        // 必传参数校验
        checkNecessaryParameter(query);

        // 查询主体sql
        StringBuilder sql = null;
        // 测点分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " t1.exception_type AS exceptionType,\n" +
                    " COUNT ( 1 ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN bcs_standard_station t2 ON t1.object_id = t2.station_number \n" +
                    " AND t2.station_source != '关键测点'\n" +
                    " LEFT JOIN bcs_sites t3 ON t2.site_id = t3.ID WHERE 1=1 ");
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        // 设备分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.EQUIPMENT.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " t1.exception_type AS exceptionType,\n" +
                    " COUNT ( 1 ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN dop_device t2 ON SPLIT_PART( t1.object_id, ',', 1 ) = t2.site_id \n" +
                    " AND SPLIT_PART( t1.object_id, ',', 2 ) = t2.code\n" +
                    " LEFT JOIN bcs_sites t3 ON SPLIT_PART( t1.object_id, ',', 1 ) = t3.ID WHERE 1=1 ");
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        // 系统分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.SYSTEM.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " t1.exception_type AS exceptionType,\n" +
                    " COUNT ( 1 ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN bcs_sites t3 ON SPLIT_PART( t1.object_id, ',', 1 ) = t3.ID WHERE 1=1 ");
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        // 厂站分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.STATION.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " t1.exception_type AS exceptionType,\n" +
                    " COUNT ( 1 ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN bcs_sites t3 ON t1.object_id = t3.ID WHERE 1=1 ");
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        sql.append(StrUtil.format(" GROUP BY t3.ID, t3.site_name, t1.exception_type ORDER BY t3.site_name, t1.exception_type"));

        // 获取分析目标异常类型数量
        List<QualityAnalysisVO> qualityAnalysisVOList = dataWarehouseBaseService.queryWithBean(null, QualityAnalysisVO.class, StrUtil.format(sql));
        
        // 在返回结果前添加过滤
        return qualityAnalysisVOList.stream()
            .filter(vo -> vo.getSiteId() != null && vo.getSiteName() != null && vo.getExceptionType() != null)
            .collect(Collectors.toList());
    }

    /**
     * 获取各厂站异常测点、设备比例TOP3系统数据
     *
     * @param query 查询条件对象，包含必要的查询参数。
     * @return 返回一个包含站点异常属性TOP3信息的列表，每个条目都是QualityAnalysisVO类型。
     */
    @Override
    public List<QualityAnalysisVO> getStationExceptionPropTOP3(QualityAnalysisQueryDTO query) {
        // 必传参数校验
        checkNecessaryParameter(query);

        // 测点分析目标查询
        Assert.isTrue((QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType()) ||
                QualityAnalysisEnum.ObjectTypeEnum.SYSTEM.getCode().equals(query.getObjectType())), "分析目标参数类型错误");

        // 稽核数量
        List<DataQualityTaskVO> dataQualityTaskVOList = new ArrayList<>();

        // 查询主体sql
        StringBuilder sql = new StringBuilder();

        // 测点分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType())) {
            // 测点稽核数量详细信息
            if (StrUtil.equals(ResourceConsts.DB_TYPE, ResourceType.DAMENG.getText())) {
                dataQualityTaskVOList = qualityAnalysisMapper.getAuditScopePointByStationAndSystemDetailDameng(query);
            } else {
                dataQualityTaskVOList = qualityAnalysisMapper.getAuditScopePointByStationAndSystemDetail(query);
            }

            sql.append("SELECT a.* FROM (")
                    .append(" SELECT ")
                    .append(" ROW_NUMBER() OVER (PARTITION BY base.siteId, base.siteName, base.systemName ")
                    .append("                   ORDER BY base.siteName DESC, base.total DESC) AS rn,")
                    .append(" base.* ")
                    .append(" FROM (")
                    .append("   SELECT")
                    .append("     t3.ID AS siteId,")
                    .append("     t3.site_name AS siteName,")
                    .append("     t2.system_name AS systemName,")
                    .append("     COUNT(DISTINCT(t1.object_id)) AS total")
                    .append("   FROM data_quality_exception t1")
                    .append("   INNER JOIN (")
                    .append("     SELECT station_number, system_name, site_id")
                    .append("     FROM bcs_standard_station")
                    .append("     WHERE station_source != '关键测点'")
                    .append("   ) t2 ON t1.object_id = t2.station_number")
                    .append("   INNER JOIN bcs_sites t3 ON t2.site_id = t3.ID")
                    .append("   WHERE 1=1");

            // 设置查询条件
            setQueryCriteria(sql, query);

            sql.append("   GROUP BY t3.ID, t3.site_name, t2.system_name")
                    .append(" ) base")
                    .append(") a WHERE a.rn <= 3")
                    .append(" ORDER BY a.siteName");
        }

        // 系统分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.SYSTEM.getCode().equals(query.getObjectType())) {
            sql.append("SELECT a.* FROM (")
                    .append(" SELECT")
                    .append("   ROW_NUMBER() OVER (")
                    .append("     PARTITION BY base.siteId, base.siteName, base.systemName")
                    .append("     ORDER BY base.siteName DESC, base.total DESC")
                    .append("   ) AS rn,")
                    .append("   base.*")
                    .append(" FROM (")
                    .append("   SELECT")
                    .append("     t3.ID AS siteId,")
                    .append("     t3.site_name AS siteName,")
                    .append("     SPLIT_PART(t1.object_id, ',', 2) AS systemName,")
                    .append("     COUNT(1) AS total")
                    .append("   FROM data_quality_exception t1")
                    .append("   INNER JOIN bcs_sites t3 ON SPLIT_PART(t1.object_id, ',', 1) = t3.ID")
                    .append("   WHERE 1=1");

            // 设置查询条件
            setQueryCriteria(sql, query);

            sql.append("   GROUP BY t3.ID, t3.site_name, SPLIT_PART(t1.object_id, ',', 2)")
                    .append(" ) base")
                    .append(") a WHERE a.rn <= 3")
                    .append(" ORDER BY a.siteName");
        }

        // 获取异常测点、系统类型数量
        List<QualityAnalysisVO> qualityAnalysisVOList = dataWarehouseBaseService.queryWithBean(null, QualityAnalysisVO.class, sql.toString());

        // 设置异常比例 (保持原有逻辑)
        if (QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType()) && !dataQualityTaskVOList.isEmpty()) {
            calculateAndSetRatios(qualityAnalysisVOList, dataQualityTaskVOList);
        }

        // 过滤无效数据
        return qualityAnalysisVOList.stream()
                .filter(vo -> vo.getSiteId() != null && vo.getSiteName() != null && vo.getSystemName() != null)
                .collect(Collectors.toList());
    }

    /**
     * 获取厂站告警数量趋势数据
     *
     * @param query 查询条件对象，包含必要的查询参数
     * @return 返回质量分析VO列表，列表中包含了每个站点的报警数量趋势信息
     */
    @Override
    public List<QualityAnalysisVO> getStationAlarmCountTrend(QualityAnalysisQueryDTO query) {

        // 必传参数校验
        checkNecessaryParameter(query);

        // 查询主体sql
        StringBuilder sql = null;

        // 测点分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " TO_CHAR( t1.start_time, 'YYYY/MM/DD' ) AS startTime,\n" +
                    " COUNT ( 1 ) total \n" +
                    " FROM\n" +
                    " data_quality_alarm t1\n" +
                    " LEFT JOIN bcs_standard_station t2 ON t1.object_id = t2.station_number \n" +
                    " AND t2.station_source != '关键测点'\n" +
                    " LEFT JOIN bcs_sites t3 ON t2.site_id = t3.ID WHERE 1=1 ");
            // 分析目标类型查询条件
            if (null != query.getObjectType()) {
                sql.append(StrUtil.format("AND t1.alarm_object_type = '{}'", query.getObjectType()));
                query.setObjectType(null);
            }
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        // 设备分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.EQUIPMENT.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " TO_CHAR( t1.start_time, 'YYYY/MM/DD' ) AS startTime,\n" +
                    " COUNT ( 1 ) total \n" +
                    " FROM\n" +
                    " data_quality_alarm t1\n" +
                    " LEFT JOIN dop_device t2 ON SPLIT_PART( t1.object_id, ',', 1 ) = t2.site_id \n" +
                    " AND SPLIT_PART( t1.object_id, ',', 2 ) = t2.code\n" +
                    " LEFT JOIN bcs_sites t3 ON SPLIT_PART( t1.object_id, ',', 1 ) = t3.ID WHERE 1=1 ");
            // 分析目标类型查询条件
            if (null != query.getObjectType()) {
                sql.append(StrUtil.format("AND t1.alarm_object_type = '{}'", query.getObjectType()));
                query.setObjectType(null);
            }
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        // 系统分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.SYSTEM.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " TO_CHAR( t1.start_time, 'YYYY/MM/DD' ) AS startTime,\n" +
                    " COUNT ( 1 ) total \n" +
                    " FROM\n" +
                    " data_quality_alarm t1\n" +
                    " LEFT JOIN bcs_sites t3 ON SPLIT_PART( t1.object_id, ',', 1 ) = t3.ID WHERE 1=1 ");
            // 分析目标类型查询条件
            if (null != query.getObjectType()) {
                sql.append(StrUtil.format("AND t1.alarm_object_type = '{}'", query.getObjectType()));
                query.setObjectType(null);
            }
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        // 厂站分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.STATION.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " TO_CHAR( t1.start_time, 'YYYY/MM/DD' ) AS startTime,\n" +
                    " COUNT ( 1 ) total \n" +
                    " FROM\n" +
                    " data_quality_alarm t1\n" +
                    " LEFT JOIN bcs_sites t3 ON t1.object_id = t3.ID WHERE 1=1 ");
            // 分析目标类型查询条件
            if (null != query.getObjectType()) {
                sql.append(StrUtil.format("AND t1.alarm_object_type = '{}'", query.getObjectType()));
                query.setObjectType(null);
            }
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        sql.append(StrUtil.format(" GROUP BY t3.ID, t3.site_name, startTime ORDER BY t3.site_name, startTime "));

        // 获取厂站告警数量
        List<QualityAnalysisVO> qualityAnalysisVOList = dataWarehouseBaseService.queryWithBean(null, QualityAnalysisVO.class, StrUtil.format(sql));

        // 在返回结果前添加过滤
        return qualityAnalysisVOList.stream()
            .filter(vo -> vo.getSiteId() != null && vo.getSiteName() != null)
            .collect(Collectors.toList());
    }

    /**
     * 获取厂站未处理告警比例数据
     *
     * @param query 查询条件对象，包含必要的查询参数
     * @return 返回质量分析VO列表，列表中包含了每个站点的未处理报警数量、总报警数量以及未处理报警比例
     */
    @Override
    public List<QualityAnalysisVO> getStationUntreatedAlarmProp(QualityAnalysisQueryDTO query) {

        // 必传参数校验
        checkNecessaryParameter(query);

        // 查询主体sql
        StringBuilder sql = null;
        // 测点分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t3.ID AS siteId, \n" +
                    " t3.site_name AS siteName, \n" +
                    " TO_CHAR( t1.start_time, 'YYYY/MM/DD' ) AS startTime, \n" +
                    " COUNT ( CASE WHEN t1.alarm_state =1 THEN 1 ELSE NULL END) AS untreated, \n" +
                    " COUNT ( 1 ) AS total, \n" +
                    " CASE WHEN COUNT ( CASE WHEN t1.alarm_state =1 THEN 1 ELSE NULL END) = 0 THEN '0'\n" +
                    " ELSE round(cast(COUNT ( CASE WHEN t1.alarm_state =1 THEN 1 ELSE NULL END) as numeric)/COUNT ( 1 ),2) END AS ratio \n" +
                    " FROM\n" +
                    " data_quality_alarm t1\n" +
                    " LEFT JOIN bcs_standard_station t2 ON t1.object_id = t2.station_number \n" +
                    " AND t2.station_source != '关键测点'\n" +
                    " LEFT JOIN bcs_sites t3 ON t2.site_id = t3.ID WHERE 1=1 ");
            // 分析目标类型查询条件
            if (null != query.getObjectType()) {
                sql.append(StrUtil.format("AND t1.alarm_object_type = '{}'", query.getObjectType()));
                query.setObjectType(null);
            }
            // 设置公共查询条件
            setQueryCriteria(sql, query);
        }
        // 设备分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.EQUIPMENT.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t3.ID AS siteId, \n" +
                    " t3.site_name AS siteName, \n" +
                    " TO_CHAR( t1.start_time, 'YYYY/MM/DD' ) AS startTime, \n" +
                    " COUNT ( CASE WHEN t1.alarm_state =1 THEN 1 ELSE NULL END) AS untreated, \n" +
                    " COUNT ( 1 ) AS total, \n" +
                    " CASE WHEN COUNT ( CASE WHEN t1.alarm_state =1 THEN 1 ELSE NULL END) = 0 THEN '0'\n" +
                    " ELSE round(cast(COUNT ( CASE WHEN t1.alarm_state =1 THEN 1 ELSE NULL END) as numeric)/COUNT ( 1 ),2) END AS ratio \n" +
                    " FROM\n" +
                    " data_quality_alarm t1\n" +
                    " LEFT JOIN dop_device t2 ON SPLIT_PART( t1.object_id, ',', 1 ) = t2.site_id \n" +
                    " AND SPLIT_PART( t1.object_id, ',', 2 ) = t2.code\n" +
                    " LEFT JOIN bcs_sites t3 ON SPLIT_PART( t1.object_id, ',', 1 ) = t3.ID WHERE 1=1 ");
            // 分析目标类型查询条件
            if (null != query.getObjectType()) {
                sql.append(StrUtil.format("AND t1.alarm_object_type = '{}'", query.getObjectType()));
                query.setObjectType(null);
            }
            // 设置公共查询条件
            setQueryCriteria(sql, query);
        }
        // 系统分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.SYSTEM.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t3.ID AS siteId, \n" +
                    " t3.site_name AS siteName, \n" +
                    " TO_CHAR( t1.start_time, 'YYYY/MM/DD' ) AS startTime, \n" +
                    " COUNT ( CASE WHEN t1.alarm_state =1 THEN 1 ELSE NULL END) AS untreated, \n" +
                    " COUNT ( 1 ) AS total, \n" +
                    " CASE WHEN COUNT ( CASE WHEN t1.alarm_state =1 THEN 1 ELSE NULL END) = 0 THEN '0'\n" +
                    " ELSE round(cast(COUNT ( CASE WHEN t1.alarm_state =1 THEN 1 ELSE NULL END) as numeric)/COUNT ( 1 ),2) END AS ratio \n" +
                    " FROM\n" +
                    " data_quality_alarm t1\n" +
                    " LEFT JOIN bcs_sites t3 ON SPLIT_PART( t1.object_id, ',', 1 ) = t3.ID WHERE 1=1 ");
            // 分析目标类型查询条件
            if (null != query.getObjectType()) {
                sql.append(StrUtil.format("AND t1.alarm_object_type = '{}'", query.getObjectType()));
                query.setObjectType(null);
            }
            // 设置公共查询条件
            setQueryCriteria(sql, query);
        }
        // 厂站分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.STATION.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t3.ID AS siteId, \n" +
                    " t3.site_name AS siteName, \n" +
                    " TO_CHAR( t1.start_time, 'YYYY/MM/DD' ) AS startTime, \n" +
                    " COUNT ( CASE WHEN t1.alarm_state =1 THEN 1 ELSE NULL END) AS untreated, \n" +
                    " COUNT ( 1 ) AS total, \n" +
                    " CASE WHEN COUNT ( CASE WHEN t1.alarm_state =1 THEN 1 ELSE NULL END) = 0 THEN '0'\n" +
                    " ELSE round(cast(COUNT ( CASE WHEN t1.alarm_state =1 THEN 1 ELSE NULL END) as numeric)/COUNT ( 1 ),2) END AS ratio \n" +
                    " FROM\n" +
                    " data_quality_alarm t1\n" +
                    " LEFT JOIN bcs_sites t3 ON t1.object_id = t3.ID WHERE 1=1 ");
            // 分析目标类型查询条件
            if (null != query.getObjectType()) {
                sql.append(StrUtil.format("AND t1.alarm_object_type = '{}'", query.getObjectType()));
                query.setObjectType(null);
            }
            // 设置公共查询条件
            setQueryCriteria(sql, query);
        }
        sql.append(StrUtil.format(" GROUP BY t3.ID, t3.site_name, startTime ORDER BY t3.site_name, startTime "));

        // 获取厂站告警未处理比率
        List<QualityAnalysisVO> qualityAnalysisVOList = dataWarehouseBaseService.queryWithBean(null, QualityAnalysisVO.class, StrUtil.format(sql));
        // 设置比率
        qualityAnalysisVOList.forEach(item -> {
            // 未处理比率
            if (StrUtil.isNotBlank(item.getRatio()) && !item.getRatio().equals("0")) {
                BigDecimal divide = new BigDecimal(item.getRatio());
                item.setRatio(divide.multiply(BigDecimal.valueOf(100)).toString().concat("%"));
            } else {
                item.setRatio("0%");
            }
        });

        // 在返回结果前添加过滤
        return qualityAnalysisVOList.stream()
            .filter(vo -> vo.getSiteId() != null && vo.getSiteName() != null)
            .collect(Collectors.toList());
    }

    /**
     * 获取厂站告警处理概况数据
     *
     * @param query 查询条件对象，包含必要的查询参数
     * @return 返回质量分析VO列表，包含了站点ID、站点名称、报警状态和报警数量
     */
    @Override
    public List<QualityAnalysisVO> getStationAlarmHandleOverview(QualityAnalysisQueryDTO query) {

        // 必传参数校验
        checkNecessaryParameter(query);

        // 查询主体sql
        StringBuilder sql = null;
        // 测点分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " t1.alarm_state AS alarmState,\n" +
                    " COUNT ( 1 ) total \n" +
                    " FROM\n" +
                    " data_quality_alarm t1\n" +
                    " LEFT JOIN bcs_standard_station t2 ON t1.object_id = t2.station_number \n" +
                    " AND t2.station_source != '关键测点'\n" +
                    " LEFT JOIN bcs_sites t3 ON t2.site_id = t3.ID WHERE 1=1 ");
            // 分析目标类型查询条件
            if (null != query.getObjectType()) {
                sql.append(StrUtil.format("AND t1.alarm_object_type = '{}'", query.getObjectType()));
                query.setObjectType(null);
            }
            // 设置公共查询条件
            setQueryCriteria(sql, query);
        }
        // 设备分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.EQUIPMENT.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " t1.alarm_state AS alarmState,\n" +
                    " COUNT ( 1 ) total \n" +
                    " FROM\n" +
                    " data_quality_alarm t1\n" +
                    " LEFT JOIN dop_device t2 ON SPLIT_PART( t1.object_id, ',', 1 ) = t2.site_id \n" +
                    " AND SPLIT_PART( t1.object_id, ',', 2 ) = t2.code\n" +
                    " LEFT JOIN bcs_sites t3 ON SPLIT_PART( t1.object_id, ',', 1 ) = t3.ID WHERE 1=1 ");
            // 分析目标类型查询条件
            if (null != query.getObjectType()) {
                sql.append(StrUtil.format("AND t1.alarm_object_type = '{}'", query.getObjectType()));
                query.setObjectType(null);
            }
            // 设置公共查询条件
            setQueryCriteria(sql, query);
        }
        // 系统分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.SYSTEM.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " t1.alarm_state AS alarmState,\n" +
                    " COUNT ( 1 ) total \n" +
                    " FROM\n" +
                    " data_quality_alarm t1\n" +
                    " LEFT JOIN bcs_sites t3 ON SPLIT_PART( t1.object_id, ',', 1 ) = t3.ID WHERE 1=1 ");
            // 分析目标类型查询条件
            if (null != query.getObjectType()) {
                sql.append(StrUtil.format("AND t1.alarm_object_type = '{}'", query.getObjectType()));
                query.setObjectType(null);
            }
            // 设置公共查询条件
            setQueryCriteria(sql, query);
        }
        // 厂站分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.STATION.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " t1.alarm_state AS alarmState,\n" +
                    " COUNT ( 1 ) total \n" +
                    " FROM\n" +
                    " data_quality_alarm t1\n" +
                    " LEFT JOIN bcs_sites t3 ON t1.object_id = t3.ID WHERE 1=1 ");
            // 分析目标类型查询条件
            if (null != query.getObjectType()) {
                sql.append(StrUtil.format("AND t1.alarm_object_type = '{}'", query.getObjectType()));
                query.setObjectType(null);
            }
            // 设置公共查询条件
            setQueryCriteria(sql, query);
        }
        sql.append(StrUtil.format(" GROUP BY t3.ID, t3.site_name, t1.alarm_state ORDER BY t3.site_name, t1.alarm_state"));

        // 获取异常处理状态数量
        List<QualityAnalysisVO> qualityAnalysisVOList = dataWarehouseBaseService.queryWithBean(null, QualityAnalysisVO.class, StrUtil.format(sql));
        // 设置告警处理状态名称
        qualityAnalysisVOList.forEach(a -> {
            a.setAlarmStateName(QualityAnalysisEnum.AlarmStateEnum.isInEnum(a.getAlarmState()) ? QualityAnalysisEnum.AlarmStateEnum.of(a.getAlarmState()).getDescription() : "");
        });

        // 在返回结果前添加过滤
        return qualityAnalysisVOList.stream()
            .filter(vo -> vo.getSiteId() != null && vo.getSiteName() != null)
            .collect(Collectors.toList());
    }

    /**
     * 获取异常处理方法及效果统计数据
     *
     * @param query 查询条件对象，包含必要的查询参数
     * @return 返回异常处理方法统计信息列表，列表中每个元素包含处理方法详情和总数
     */
    @Override
    public List<QualityAnalysisVO> getExceptionProcessingMethodStatistics(QualityAnalysisQueryDTO query) {

        // 必传参数校验
        checkNecessaryParameter(query);
        // 测点分析目标查询
        Assert.isTrue(QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType()), "分析目标参数类型错误");

        // 查询主体sql
        StringBuilder sql = new StringBuilder(" SELECT\n" +
                " t1.handle_detail AS handleDetail,\n" +
                " COUNT ( 1 ) AS total \n" +
                " FROM\n" +
                " data_quality_exception t1\n" +
                " LEFT JOIN bcs_standard_station t2 ON t1.object_id = t2.station_number \n" +
                " AND t2.station_source != '关键测点'\n" +
                " LEFT JOIN bcs_sites t3 ON t2.site_id = t3.ID WHERE 1=1");

        // 设置公共查询条件
        setQueryCriteria(sql, query);
        sql.append(StrUtil.format(" GROUP BY t1.handle_detail ORDER BY t1.handle_detail "));

        // 获取异常处理方法对应测点个数
        List<QualityAnalysisVO> qualityAnalysisVOList = dataWarehouseBaseService.queryWithBean(null, QualityAnalysisVO.class, StrUtil.format(sql));
        return qualityAnalysisVOList;
    }

    /**
     * 获取测点、设备异常率趋势详情
     *
     * @param query 查询条件对象，包含分析目标类型、时间范围等条件
     * @return 返回异常计数趋势的详细列表，其中包含每个时间点的异常总数、站点信息等
     */
    @Override
    public List<QualityAnalysisVO> getExceptionCountTrendDetail(QualityAnalysisQueryDTO query) {

        // 必传参数校验
        checkNecessaryParameter(query);

        // 稽核对象数量信息
        List<DataQualityTaskVO> taskVOList = new ArrayList<>();

        // 测点、设备分析目标查询
        Assert.isTrue((QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType()) ||
                QualityAnalysisEnum.ObjectTypeEnum.EQUIPMENT.getCode().equals(query.getObjectType())), "分析目标参数类型错误");

        // 统计sql主体
        StringBuilder sql = null;
        // 测点分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType())) {
            // 测点稽核数量详情
            taskVOList = qualityAnalysisMapper.getAuditScopeByPointDetail(query);

            sql = new StringBuilder("SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " TO_CHAR( t1.start_time, 'YYYY-MM-DD' ) AS startTime,\n" +
                    " COUNT ( DISTINCT ( t1.object_id ) ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN bcs_standard_station t2 ON t1.object_id = t2.station_number \n" +
                    " AND t2.station_source != '关键测点'\n" +
                    " LEFT JOIN bcs_sites t3 ON t2.site_id = t3.id WHERE 1=1 AND t3.ID IS NOT NULL ");  // 添加过滤条件
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        // 设备分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.EQUIPMENT.getCode().equals(query.getObjectType())) {
            // 设备稽核数量详情
            taskVOList = qualityAnalysisMapper.getAuditScopeByDeviceDetail(query);

            sql = new StringBuilder(" SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " TO_CHAR( t1.start_time, 'YYYY-MM-DD' ) AS startTime,\n" +
                    " COUNT ( DISTINCT ( t1.object_id ) ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN dop_device t2 ON SPLIT_PART( t1.object_id, ',', 1 ) = t2.site_id \n" +
                    " AND SPLIT_PART( t1.object_id, ',', 2 ) = t2.code\n" +
                    " LEFT JOIN bcs_sites t3 ON SPLIT_PART( t1.object_id, ',', 1 ) = t3.ID WHERE 1=1 ");
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        sql.append(StrUtil.format(" GROUP BY t3.ID, t3.site_name, startTime ORDER BY t3.site_name, startTime "));
        // 获取异常统计数据
        List<QualityAnalysisVO> qualityAnalysisVOList = dataWarehouseBaseService.queryWithBean(null, QualityAnalysisVO.class, StrUtil.format(sql));

        // 设置异常比率和稽核类型数量
        for (QualityAnalysisVO a : qualityAnalysisVOList) {
            // 设置默认比率
            a.setRatio("0%");
            // 设置默认稽核测点、设备数量
            a.setAuditRange(0);

            // 查找对应的稽核数据
            for (DataQualityTaskVO taskVO : taskVOList) {
                if (taskVO.getSiteId().equals(a.getSiteId())) {
                    a.setAuditRange(taskVO.getAuditScope());
                    // 分子分母不为0则计算比率
                    if (Objects.nonNull(a.getTotal()) && a.getTotal() > 0 && taskVO.getAuditScope() > 0) {
                        BigDecimal ratio = new BigDecimal(a.getTotal())
                                .multiply(BigDecimal.valueOf(100))
                                .divide(new BigDecimal(taskVO.getAuditScope()), 2, RoundingMode.HALF_UP);
                        a.setRatio(ratio.toString() + "%");
                    }
                    break;
                }
            }
        }

        // 过滤掉siteId为空的记录
        return qualityAnalysisVOList.stream()
                .filter(vo -> vo.getSiteId() != null && vo.getSiteName() != null)
                .collect(Collectors.toList());
    }

    /**
     * 获取异常数量趋势详情
     *
     * @param query 查询条件对象，包含分析目标类型、起始时间、结束时间等条件
     * @return 异常计数趋势详情列表，列表中每个项包含厂站ID、厂站名称、日期和异常总数
     */
    @Override
    public List<QualityAnalysisVO> getAbnormalCountTrendDetail(QualityAnalysisQueryDTO query) {

        // 必传参数校验
        checkNecessaryParameter(query);

        // 统计sql主体
        StringBuilder sql = null;
        // 测点分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder("SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " TO_CHAR( t1.start_time, 'YYYY-MM-DD' ) AS startTime,\n" +
                    " COUNT ( 1 ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN bcs_standard_station t2 ON t1.object_id = t2.station_number \n" +
                    " AND t2.station_source != '关键测点'\n" +
                    " LEFT JOIN bcs_sites t3 ON t2.site_id = t3.ID WHERE 1=1 ");
            // 设置固定日期格式查询条件
            if (StrUtil.isNotBlank(query.getSearchStartTime()) && StrUtil.isNotBlank(query.getSearchEndTime())) {
                sql.append(StrUtil.format(" AND ( TO_CHAR( t1.start_time, 'YYYY-MM-DD' ) BETWEEN '{}' AND '{}' )", query.getSearchStartTime(), query.getSearchEndTime()));
                query.setSearchStartTime(null).setSearchEndTime(null);
            }
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        // 设备分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.EQUIPMENT.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " TO_CHAR( t1.start_time, 'YYYY-MM-DD' ) AS startTime,\n" +
                    " COUNT ( 1 ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN dop_device t2 ON SPLIT_PART( t1.object_id, ',', 1 ) = t2.site_id \n" +
                    " AND SPLIT_PART( t1.object_id, ',', 2 ) = t2.code\n" +
                    " LEFT JOIN bcs_sites t3 ON SPLIT_PART( t1.object_id, ',', 1 ) = t3.ID WHERE 1=1 ");
            // 设置固定日期格式查询条件
            if (StrUtil.isNotBlank(query.getSearchStartTime()) && StrUtil.isNotBlank(query.getSearchEndTime())) {
                sql.append(StrUtil.format(" AND ( TO_CHAR( t1.start_time, 'YYYY-MM-DD' ) BETWEEN '{}' AND '{}' )", query.getSearchStartTime(), query.getSearchEndTime()));
                query.setSearchStartTime(null).setSearchEndTime(null);
            }
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        // 系统分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.SYSTEM.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " TO_CHAR( t1.start_time, 'YYYY-MM-DD' ) AS startTime,\n" +
                    " COUNT ( 1 ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN bcs_sites t3 ON SPLIT_PART( t1.object_id, ',', 1 ) = t3.ID WHERE 1=1 ");
            // 设置固定日期格式查询条件
            if (StrUtil.isNotBlank(query.getSearchStartTime()) && StrUtil.isNotBlank(query.getSearchEndTime())) {
                sql.append(StrUtil.format(" AND ( TO_CHAR( t1.start_time, 'YYYY-MM-DD' ) BETWEEN '{}' AND '{}' )", query.getSearchStartTime(), query.getSearchEndTime()));
                query.setSearchStartTime(null).setSearchEndTime(null);
            }
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        // 厂站分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.STATION.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " TO_CHAR( t1.start_time, 'YYYY-MM-DD' ) AS startTime,\n" +
                    " COUNT ( 1 ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN bcs_sites t3 ON t1.object_id = t3.ID WHERE 1=1 ");
            // 设置固定日期格式查询条件
            if (StrUtil.isNotBlank(query.getSearchStartTime()) && StrUtil.isNotBlank(query.getSearchEndTime())) {
                sql.append(StrUtil.format(" AND ( TO_CHAR( t1.start_time, 'YYYY-MM-DD' ) BETWEEN '{}' AND '{}' )", query.getSearchStartTime(), query.getSearchEndTime()));
                query.setSearchStartTime(null).setSearchEndTime(null);
            }
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        sql.append(StrUtil.format(" GROUP BY t3.ID, t3.site_name, startTime ORDER BY t3.site_name, startTime "));

        // 获取异常数量
        List<QualityAnalysisVO> qualityAnalysisVOList = dataWarehouseBaseService.queryWithBean(null, QualityAnalysisVO.class, StrUtil.format(sql));
        // 添加过滤条件,排除siteId或siteName为空的记录
        return qualityAnalysisVOList.stream()
                .filter(vo -> vo.getSiteId() != null && vo.getSiteName() != null)
                .collect(Collectors.toList());
    }

    /**
     * 根据分析类型查询异常类型数量趋势详情
     *
     * @param query 查询条件对象，包含分析目标类型、起始时间、结束时间等条件
     * @return 异常类型数量趋势详情列表，包含异常类型、发生时间及数量
     */
    @Override
    public List<QualityAnalysisVO> getAbnormalTypeCountTrendDetail(QualityAnalysisQueryDTO query) {

        // 必传参数校验
        checkNecessaryParameter(query);

        // 统计sql主体
        StringBuilder sql = null;
        // 测点分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder("SELECT\n" +
                    " t1.exception_type AS exceptionType,\n" +
                    " TO_CHAR( t1.start_time, 'YYYY-MM-DD' ) AS startTime,\n" +
                    " COUNT ( 1 ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN bcs_standard_station t2 ON t1.object_id = t2.station_number \n" +
                    " AND t2.station_source != '关键测点'\n" +
                    " LEFT JOIN bcs_sites t3 ON t2.site_id = t3.ID WHERE 1=1 ");
            // 设置固定日期格式查询条件
            if (StrUtil.isNotBlank(query.getSearchStartTime()) && StrUtil.isNotBlank(query.getSearchEndTime())) {
                sql.append(StrUtil.format(" AND ( TO_CHAR( t1.start_time, 'YYYY-MM-DD' ) BETWEEN '{}' AND '{}' )", query.getSearchStartTime(), query.getSearchEndTime()));
                query.setSearchStartTime(null).setSearchEndTime(null);
            }
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        // 设备分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.EQUIPMENT.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder("SELECT\n" +
                    " t1.exception_type AS exceptionType,\n" +
                    " TO_CHAR( t1.start_time, 'YYYY-MM-DD' ) AS startTime,\n" +
                    " COUNT ( 1 ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN dop_device t2 ON SPLIT_PART( t1.object_id, ',', 1 ) = t2.site_id \n" +
                    " AND SPLIT_PART( t1.object_id, ',', 2 ) = t2.code\n" +
                    " LEFT JOIN bcs_sites t3 ON SPLIT_PART( t1.object_id, ',', 1 ) = t3.ID WHERE 1=1 ");
            // 设置固定日期格式查询条件
            if (StrUtil.isNotBlank(query.getSearchStartTime()) && StrUtil.isNotBlank(query.getSearchEndTime())) {
                sql.append(StrUtil.format(" AND ( TO_CHAR( t1.start_time, 'YYYY-MM-DD' ) BETWEEN '{}' AND '{}' )", query.getSearchStartTime(), query.getSearchEndTime()));
                query.setSearchStartTime(null).setSearchEndTime(null);
            }
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        // 系统分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.SYSTEM.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t1.exception_type AS exceptionType,\n" +
                    " TO_CHAR( t1.start_time, 'YYYY-MM-DD' ) AS startTime,\n" +
                    " COUNT ( 1 ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN bcs_sites t3 ON SPLIT_PART( t1.object_id, ',', 1 ) = t3.ID WHERE 1=1 ");
            // 设置固定日期格式查询条件
            if (StrUtil.isNotBlank(query.getSearchStartTime()) && StrUtil.isNotBlank(query.getSearchEndTime())) {
                sql.append(StrUtil.format(" AND ( TO_CHAR( t1.start_time, 'YYYY-MM-DD' ) BETWEEN '{}' AND '{}' )", query.getSearchStartTime(), query.getSearchEndTime()));
                query.setSearchStartTime(null).setSearchEndTime(null);
            }
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        // 厂站分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.STATION.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t1.exception_type AS exceptionType,\n" +
                    " TO_CHAR( t1.start_time, 'YYYY-MM-DD' ) AS startTime,\n" +
                    " COUNT ( 1 ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN bcs_sites t3 ON t1.object_id = t3.ID WHERE 1=1 ");
            // 设置固定日期格式查询条件
            if (StrUtil.isNotBlank(query.getSearchStartTime()) && StrUtil.isNotBlank(query.getSearchEndTime())) {
                sql.append(StrUtil.format(" AND ( TO_CHAR( t1.start_time, 'YYYY-MM-DD' ) BETWEEN '{}' AND '{}' )", query.getSearchStartTime(), query.getSearchEndTime()));
                query.setSearchStartTime(null).setSearchEndTime(null);
            }
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        sql.append(StrUtil.format(" GROUP BY t1.exception_type, startTime ORDER BY t1.exception_type, startTime"));

        // 获取异常分类数量
        List<QualityAnalysisVO> qualityAnalysisVOList = dataWarehouseBaseService.queryWithBean(null, QualityAnalysisVO.class, StrUtil.format(sql));
        // 设置属性内容
        qualityAnalysisVOList.forEach(qualityAnalysisVO -> {
            // 异常级别信息
            qualityAnalysisVO.setExceptionObjectType(query.getObjectType());
            qualityAnalysisVO.setExceptionObjectTypeName(QualityAnalysisEnum.ObjectTypeEnum.of(query.getObjectType()).getDescription());
        });

        return qualityAnalysisVOList;
    }

    /**
     * 获取分类异常比例详情
     *
     * @param query 查询条件，包含分析目标、时间范围等信息
     * @return 返回异常比例分析的详情列表
     */
    @Override
    public List<QualityAnalysisVO> getClassifyExceptionRatioDetail(QualityAnalysisQueryDTO query) {

        // 必传参数校验
        checkNecessaryParameter(query);
        // 测点分析目标查询
        Assert.isTrue(QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType()), "分析目标参数类型错误");

        // 统计sql主体
        StringBuilder sql = new StringBuilder(" SELECT \n" +
                " CASE \n" +
                " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CY' THEN '振摆'\n" +
                " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CT' THEN '温度'\n" +
                " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CS' THEN '速率'\n" +
                " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CP' THEN '压力'\n" +
                " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CM' THEN '湿度'\n" +
                " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CL' THEN '液位'\n" +
                " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CK' THEN '时间'\n" +
                " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CG' THEN '位置'\n" +
                " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CF' THEN '流量'\n" +
                " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CE' THEN '电气变量'\n" +
                " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CD' THEN '密度'\n" +
                " ELSE '其他' \n" +
                " END AS pointTypeName," +
                " COUNT ( DISTINCT ( t1.object_id ) ) AS total \n" +
                " FROM\n" +
                " data_quality_exception t1\n" +
                " LEFT JOIN bcs_standard_station t2 ON t1.object_id = t2.station_number \n" +
                " AND t2.station_source != '关键测点'\n" +
                " LEFT JOIN bcs_sites t3 ON t2.site_id = t3.ID WHERE 1=1 ");
        // 设置查询条件
        setQueryCriteria(sql, query);
        sql.append(StrUtil.format(" GROUP BY pointTypeName ORDER BY pointTypeName DESC "));

        // 获取异常测点数量
        List<QualityAnalysisVO> qualityAnalysisVOList = dataWarehouseBaseService.queryWithBean(null, QualityAnalysisVO.class, StrUtil.format(sql));

        // 测点稽核信息
        List<DataQualityTaskVO> taskVOList = qualityAnalysisMapper.getAuditScopeByPointTypeDetail(query);
        // 不分电站
        Map<String, Integer> map = taskVOList.stream().collect(Collectors.groupingBy(a -> a.getPointTypeName(), Collectors.summingInt(a -> a.getAuditScope())));

        // 设置异常比率
        qualityAnalysisVOList.forEach(a -> {
            // 设置默认稽核数量
            a.setAuditRange(0);
            // 设置默认比例
            a.setRatio("0.0%");
            for (String s : map.keySet()) {
                if (a.getPointTypeName().equals(s)) {
                    a.setAuditRange(map.get(s));
                    // 分子分母不为0则计算比率
                    if (Objects.nonNull(a.getTotal()) && a.getTotal() > 0 && map.get(s) > 0) {
                        BigDecimal divide = new BigDecimal(a.getTotal()).divide(new BigDecimal(map.get(s)), 2, RoundingMode.HALF_UP);
                        a.setRatio(divide.multiply(BigDecimal.valueOf(100)).toString().concat("%"));
                        break;
                    }
                }
            }
        });

        return qualityAnalysisVOList;
    }

    /**
     * 获取异常点类型分布详情
     *
     * @param query 查询条件对象，包含必要的查询参数
     * @return 返回异常点类型分布的详细列表，列表中每个元素包含异常类型、测点类型和总数
     */
    @Override
    public List<QualityAnalysisVO> getExceptionPointTypeDistributionDetail(QualityAnalysisQueryDTO query) {

        // 必传参数校验
        checkNecessaryParameter(query);
        // 测点分析目标查询
        Assert.isTrue(QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType()), "分析目标参数类型错误");

        // 查询主体sql
        StringBuilder sql = new StringBuilder("SELECT\n" +
                " t1.exception_type AS exceptionType,\n" +
                " CASE \n" +
                " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CY' THEN '振摆'\n" +
                " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CT' THEN '温度'\n" +
                " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CS' THEN '速率'\n" +
                " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CP' THEN '压力'\n" +
                " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CM' THEN '湿度'\n" +
                " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CL' THEN '液位'\n" +
                " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CK' THEN '时间'\n" +
                " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CG' THEN '位置'\n" +
                " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CF' THEN '流量'\n" +
                " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CE' THEN '电气变量'\n" +
                " WHEN SUBSTRING( t1.object_id, 11, 2 ) = 'CD' THEN '密度'\n" +
                " ELSE '其他' \n" +
                " END AS pointTypeName," +
                " COUNT ( 1 ) AS total \n" +
                " FROM\n" +
                " data_quality_exception t1\n" +
                " LEFT JOIN bcs_standard_station t2 ON t1.object_id = t2.station_number \n" +
                " AND t2.station_source != '关键测点'\n" +
                " LEFT JOIN bcs_sites t3 ON t2.site_id = t3.ID WHERE 1=1 ");
        // 设置查询条件
        setQueryCriteria(sql, query);
        sql.append(StrUtil.format(" GROUP BY t1.exception_type, pointTypeName ORDER BY t1.exception_type, pointTypeName DESC "));

        // 获取异常测点类型数量
        List<QualityAnalysisVO> qualityAnalysisVOList = dataWarehouseBaseService.queryWithBean(null, QualityAnalysisVO.class, StrUtil.format(sql));
        return qualityAnalysisVOList;
    }

    /**
     * 获取稽核分析目标分类型统计详情
     *
     * @param query 查询条件对象，包含分析目标类型、站点ID、时间范围等条件
     * @return 分析目标统计详情列表，包含每个测点或设备的异常数量、类型名称等信息
     */
    @Override
    public List<DataQualityTaskVO> getAuditTypeStatisticsDetail(QualityAnalysisQueryDTO query) {

        // 必传参数校验
        checkNecessaryParameter(query);

        // 系统分析目标不展示接口数据
        Assert.isTrue((QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType()) ||
                QualityAnalysisEnum.ObjectTypeEnum.EQUIPMENT.getCode().equals(query.getObjectType())), "分析目标参数类型错误");

        // 接口返回数据
        List<DataQualityTaskVO> auditPointsCounts = new ArrayList<>();
        // 测点分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType())) {
            auditPointsCounts = qualityAnalysisMapper.getAuditScopeByPointTypeDetail(query);
        }
        // 设备分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.EQUIPMENT.getCode().equals(query.getObjectType())) {
            auditPointsCounts = qualityAnalysisMapper.getAuditScopeByStationAndDeviceDetail(query);
        }
        return auditPointsCounts;
    }

    /**
     * 根据分析目标查询异常类型统计详情
     *
     * @param query 查询条件，包含分析目标类型、时间范围等
     * @return 返回异常类型统计列表，包含每个异常类型的数量
     */
    @Override
    public List<QualityAnalysisVO> getExceptionTypeStatisticsDetail(QualityAnalysisQueryDTO query) {

        // 必传参数校验
        checkNecessaryParameter(query);

        // 查询主体sql
        StringBuilder sql = null;
        // 测点分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " t1.exception_type AS exceptionType,\n" +
                    " COUNT ( 1 ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN bcs_standard_station t2 ON t1.object_id = t2.station_number \n" +
                    " AND t2.station_source != '关键测点'\n" +
                    " LEFT JOIN bcs_sites t3 ON t2.site_id = t3.ID WHERE 1=1 ");
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        // 设备分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.EQUIPMENT.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " t1.exception_type AS exceptionType,\n" +
                    " COUNT ( 1 ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN dop_device t2 ON SPLIT_PART( t1.object_id, ',', 1 ) = t2.site_id \n" +
                    " AND SPLIT_PART( t1.object_id, ',', 2 ) = t2.code\n" +
                    " LEFT JOIN bcs_sites t3 ON SPLIT_PART( t1.object_id, ',', 1 ) = t3.ID WHERE 1=1 ");
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        // 系统分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.SYSTEM.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " t1.exception_type AS exceptionType,\n" +
                    " COUNT ( 1 ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN bcs_sites t3 ON SPLIT_PART( t1.object_id, ',', 1 ) = t3.ID WHERE 1=1 ");
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        // 厂站分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.STATION.getCode().equals(query.getObjectType())) {
            sql = new StringBuilder(" SELECT\n" +
                    " t3.ID AS siteId,\n" +
                    " t3.site_name AS siteName,\n" +
                    " t1.exception_type AS exceptionType,\n" +
                    " COUNT ( 1 ) AS total \n" +
                    " FROM\n" +
                    " data_quality_exception t1\n" +
                    " LEFT JOIN bcs_sites t3 ON t1.object_id = t3.ID WHERE 1=1 ");
            // 设置查询条件
            setQueryCriteria(sql, query);
        }
        sql.append(StrUtil.format(" GROUP BY t3.ID, t3.site_name, t1.exception_type ORDER BY t3.site_name, t1.exception_type"));

        // 获取分析目标异常类型数量
        List<QualityAnalysisVO> qualityAnalysisVOList = dataWarehouseBaseService.queryWithBean(null, QualityAnalysisVO.class, StrUtil.format(sql));
        
        // 添加过滤,排除siteId或siteName为空的记录
        return qualityAnalysisVOList.stream()
                .filter(vo -> vo.getSiteId() != null && vo.getSiteName() != null)
                .collect(Collectors.toList());
    }

    /**
     * 获取站点或系统异常属性TOP3详情
     *
     * @param query 查询条件对象，包含分析目标类型、条件等信息
     * @return 返回质量分析视图对象列表，包含站点、系统名称、异常总数、稽核数量及比例等信息
     */
    @Override
    public List<QualityAnalysisVO> getStationExceptionPropTOP3Detail(QualityAnalysisQueryDTO query) {
        // 必传参数校验
        checkNecessaryParameter(query);

        // 测点、系统分析目标查询
        Assert.isTrue((QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType()) ||
                QualityAnalysisEnum.ObjectTypeEnum.SYSTEM.getCode().equals(query.getObjectType())), "分析目标参数类型错误");

        // 稽核数量
        List<DataQualityTaskVO> dataQualityTaskVOList = new ArrayList<>();

        // 查询主体sql
        StringBuilder sql = new StringBuilder();

        // 测点分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType())) {
            // 测点稽核数量详细信息
            if (StrUtil.equals(ResourceConsts.DB_TYPE, ResourceType.DAMENG.getText())) {
                dataQualityTaskVOList = qualityAnalysisMapper.getAuditScopePointByStationAndSystemDetailDameng(query);
            } else {
                dataQualityTaskVOList = qualityAnalysisMapper.getAuditScopePointByStationAndSystemDetail(query);
            }

            sql.append("SELECT a.* FROM (")
                    .append(" SELECT ")
                    .append(" ROW_NUMBER() OVER (PARTITION BY base.siteId, base.siteName, base.systemName ")
                    .append("                   ORDER BY base.siteName DESC, base.total DESC) AS rn,")
                    .append(" base.* ")
                    .append(" FROM (")
                    .append("   SELECT")
                    .append("     t3.ID AS siteId,")
                    .append("     t3.site_name AS siteName,")
                    .append("     t2.system_name AS systemName,")
                    .append("     COUNT(DISTINCT(t1.object_id)) AS total")
                    .append("   FROM data_quality_exception t1")
                    .append("   INNER JOIN (")
                    .append("     SELECT station_number, system_name, site_id")
                    .append("     FROM bcs_standard_station")
                    .append("     WHERE station_source != '关键测点'")
                    .append("   ) t2 ON t1.object_id = t2.station_number")
                    .append("   INNER JOIN bcs_sites t3 ON t2.site_id = t3.ID")
                    .append("   WHERE 1=1");

            // 设置查询条件
            setQueryCriteria(sql, query);

            sql.append("   GROUP BY t3.ID, t3.site_name, t2.system_name")
                    .append(" ) base")
                    .append(") a WHERE a.rn <= 3")
                    .append(" ORDER BY a.siteName");
        }

        // 系统分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.SYSTEM.getCode().equals(query.getObjectType())) {
            // 系统稽核数量详细信息
            dataQualityTaskVOList = qualityAnalysisMapper.getAuditScopeSystemByStationAndSystemDetail(query);

            sql.append("SELECT a.siteId, a.siteName, a.systemName, a.total FROM (")
                    .append(" SELECT")
                    .append("   ROW_NUMBER() OVER (")
                    .append("     PARTITION BY base.siteName, base.systemName")
                    .append("     ORDER BY base.total DESC")
                    .append("   ) AS rn,")
                    .append("   base.*")
                    .append(" FROM (")
                    .append("   SELECT")
                    .append("     t3.ID AS siteId,")
                    .append("     t3.site_name AS siteName,")
                    .append("     SPLIT_PART(t1.object_id, ',', 2) AS systemName,")
                    .append("     COUNT(1) AS total")
                    .append("   FROM data_quality_exception t1")
                    .append("   INNER JOIN bcs_sites t3 ON SPLIT_PART(t1.object_id, ',', 1) = t3.ID")
                    .append("   WHERE 1=1");

            // 设置查询条件
            setQueryCriteria(sql, query);

            sql.append("   GROUP BY t3.ID, t3.site_name, SPLIT_PART(t1.object_id, ',', 2)")
                    .append(" ) base")
                    .append(") a WHERE a.rn <= 3")
                    .append(" ORDER BY a.siteName");
        }

        // 获取异常测点、系统类型数量
        List<QualityAnalysisVO> qualityAnalysisVOList = dataWarehouseBaseService.queryWithBean(null, QualityAnalysisVO.class, sql.toString());

        // 设置异常比例 (保持原有逻辑)
        if (QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType()) && !dataQualityTaskVOList.isEmpty()) {
            calculateAndSetRatios(qualityAnalysisVOList, dataQualityTaskVOList);
        }

        // 过滤无效数据
        return qualityAnalysisVOList.stream()
                .filter(vo -> vo.getSiteId() != null && vo.getSiteName() != null && vo.getSystemName() != null)
                .collect(Collectors.toList());
    }

    // 抽取计算比率的逻辑到单独的方法
    private void calculateAndSetRatios(List<QualityAnalysisVO> analysisVOList, List<DataQualityTaskVO> taskVOList) {
        // 先对taskVOList进行分组聚合
        Map<String, Map<String, Integer>> siteSystemAuditMap = taskVOList.stream()
            .collect(Collectors.groupingBy(
                DataQualityTaskVO::getSiteId,
                Collectors.groupingBy(
                    DataQualityTaskVO::getSystemName,
                    Collectors.summingInt(DataQualityTaskVO::getAuditScope)
                )
            ));

        // 计算比率
        for (QualityAnalysisVO analysisVO : analysisVOList) {
            analysisVO.setRatio("0.0%");
            
            if (analysisVO.getSiteId() != null && analysisVO.getSystemName() != null) {
                Integer totalAuditScope = siteSystemAuditMap
                    .getOrDefault(analysisVO.getSiteId(), Collections.emptyMap())
                    .get(analysisVO.getSystemName());
                
                if (totalAuditScope != null && totalAuditScope > 0 && analysisVO.getTotal() != null) {
                    BigDecimal ratio = new BigDecimal(analysisVO.getTotal())
                        .divide(new BigDecimal(totalAuditScope), 2, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100));
                    analysisVO.setRatio(ratio.toString() + "%");
                }
            }
        }
    }

    /**
     * 检查必要的参数是否正确。
     * 该方法确保了传入的QualityAnalysisQueryDTO对象中objectType字段不为空，并且其值是QualityAnalysisEnum.ObjectTypeEnum枚举中的有效值。
     *
     * @param query QualityAnalysisQueryDTO对象，包含需要检查的参数。
     * @throws IllegalArgumentException 如果objectType为null或不属于枚举范围，则抛出此异常。
     */
    private void checkNecessaryParameter(QualityAnalysisQueryDTO query) {

        Assert.notNull(query.getObjectType(), "objectType is null");
        Assert.isTrue(QualityAnalysisEnum.ObjectTypeEnum.isInEnum(query.getObjectType()), "objectType is error");
    }

    /**
     * 根据提供的查询条件设置查询标准
     *
     * @param sql   用于构建查询语句的StringBuilder对象
     * @param query 包含各种查询条件的QualityAnalysisQueryDTO对象
     */
    private void setQueryCriteria(StringBuilder sql, QualityAnalysisQueryDTO query) {

        // 分析目标类型查询条件
        if (null != query.getObjectType()) {
            sql.append(StrUtil.format(" AND t1.exception_object_type = '{}'", query.getObjectType()));
        }
        // 厂站查询条件
        if (StrUtil.isNotBlank(query.getSiteId())) {
            sql.append(StrUtil.format(" AND t3.id = '{}'", query.getSiteId()));
        }
        // 时间范围查询条件
        if (StrUtil.isNotBlank(query.getSearchStartTime()) && StrUtil.isNotBlank(query.getSearchEndTime())) {
            sql.append(StrUtil.format(" AND ( TO_CHAR( t1.start_time, 'YYYY-MM-DD' ) BETWEEN '{}' AND '{}')", query.getSearchStartTime(), query.getSearchEndTime()));
        }
        // 异常类型查询条件
        if (StrUtil.isNotBlank(query.getExceptionType())) {
            sql.append(StrUtil.format(" AND t1.exception_type = '{}'", query.getExceptionType()));
        }

        // 测点分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.POINTS.getCode().equals(query.getObjectType())) {
            // 系统名称查询条件
            if (StrUtil.isNotBlank(query.getSystemName())) {
                sql.append(StrUtil.format(" AND t2.system_name = '{}' ", query.getSystemName()));
            }
            // 测点类型查询条件
            if (StrUtil.isNotBlank(query.getPointType())) {
                // 其他测点类型查询条件特殊处理
                if (QualityAnalysisEnum.PointTypeEnum.OTHER.getCode().equals(query.getPointType())) {
                    sql.append(StrUtil.format(" AND SUBSTRING(t1.object_id, 11, 2) NOT IN ('CY', 'CT', 'CS', 'CP' ,'CM' ,'CL' ,'CK', 'CG', 'CF', 'CE', 'CD')"));
                } else {
                    sql.append(StrUtil.format(" AND SUBSTRING(t1.object_id, 11, 2) = '{}'", query.getPointType()));
                }
            }
            // 逻辑测点查询条件
            if (StrUtil.isNotBlank(query.getLogicPoint())) {
                sql.append(StrUtil.format(" AND t2.cn_name = '{}'", query.getLogicPoint()));
            }
        }
        // 设备分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.EQUIPMENT.getCode().equals(query.getObjectType())) {
            // 逻辑设备查询条件
            if (StrUtil.isNotBlank(query.getLogicPath())) {
                // 逻辑设备为其他查询条件特殊处理
                if (QualityAnalysisEnum.PrimaryDeviceEnum.OTHER.getLogicPath().equals(query.getLogicPath())) {
                    sql.append(StrUtil.format(" AND t2.logic_path NOT LIKE '{}' " +
                                    "AND t2.logic_path NOT LIKE '{}' " +
                                    "AND t2.logic_path NOT LIKE '{}' " +
                                    "AND t2.logic_path NOT LIKE '{}' " +
                                    "AND t2.logic_path NOT LIKE '{}' " +
                                    "AND t2.logic_path NOT LIKE '{}' " +
                                    "AND t2.logic_path NOT LIKE '{}' ",
                            "水轮发电机组~水轮机系统~水轮机%",
                            "水轮发电机组~发电机系统~发电机%",
                            "电力输出和厂用电~电力输出系统~主变压器系统%",
                            "电网和配电系统~500kV系统~500kV出现开关及开关间隔%",
                            "电网和配电系统~500kV系统~500kV输电间隔系统%",
                            "电网和配电系统~220kV输电系统%",
                            "电网和配电系统~500kV系统~500kV出线场系统~GIL系统~GIL%"));
                } else {
                    // 多逻辑路径处理
                    String[] logicPath = query.getLogicPath().split(",");
                    StringBuilder whereSql = new StringBuilder(" AND (");
                    for (int i = 0; i < logicPath.length; i++) {
                        whereSql.append(StrUtil.format(" ( t2.logic_path = '{}' OR t2.logic_path LIKE '{}' ) ", logicPath[i], logicPath[i] + "~%"));
                        if (i < logicPath.length - 1) {
                            whereSql.append(" OR");
                        }
                    }
                    whereSql.append(")");
                    sql.append(whereSql);
                }
            }
        }
        // 系统分析目标
        if (QualityAnalysisEnum.ObjectTypeEnum.SYSTEM.getCode().equals(query.getObjectType())) {
            // 系统名称查询条件
            if (StrUtil.isNotBlank(query.getSystemName())) {
                sql.append(StrUtil.format(" AND SPLIT_PART( t1.object_id, ',', 2 ) = '{}' ", query.getSystemName()));
            }
        }
    }
}
