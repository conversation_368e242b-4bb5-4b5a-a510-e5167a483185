package com.dhcc.dsp.business.dataquality_new.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class DataQualityTaskVO implements Serializable {

    private static final long serialVersionUID = 1L;
    private String id;
    private String taskName;
    private String taskDesc;
    private String siteId;
    private String createUser;
    private String createTime;
    private String updateTime;
    private Integer status;
    private Integer executeType;
    private String executeCron;
    private String libraryId;
    private String userName;
    private String libraryName;
    private Integer auditObjectType;
    private Integer objectCount;
    private Integer stationCount;
    private Integer missingParamCount;
    private String flowTaskId;
    private String flowServeId;
    private String auditProId;
    private String exceptionName;
    private String executeCronName;
    private String lastExecuteTime;
    private Integer flowStatus;
    private Integer auditProType;

    /**
     * 测点类型
     */
    private String pointType;

    /**
     * 测点类型名称
     */
    private String pointTypeName;

    /**
     * 厂站名称
     */
    private String siteName;

    /**
     * 主设备名称
     */
    private String logicName;

    /**
     * 系统名称
     */
    private String systemName;

    /**
     * 异常对象稽核范围
     */
    private Integer auditScope;

    private Integer offsetTime;//1-当前时间，2-前推15分钟，3-前推60分钟，4-小时0分，5-当天0点，6-当月0点

    private Integer timeRange;

    private Integer timeRangeUnit;//1-秒，2-分，3-时，4-天，5-月，6-自然月
}
