<?xml version="1.0" encoding="utf-8" ?>
<properties>
    <property name="createIndices">
        <![CDATA[{
          "settings": {
            "number_of_shards": 6,
            "index.refresh_interval": "5s",
            "index.mapping.ignore_malformed": true,
            "analysis": {
              "analyzer": {
                "my_analyzer": {
                  "type": "ik_max_word"
                }
              }
            }
          },
          "mappings": {
            "dynamic_templates": [
              {
                "strings": {
                  "match_mapping_type": "string",
                  "mapping": {
                    "type": "text",
                    "analyzer": "my_analyzer",
                    "fields": {
                      "keyword": {
                        "type": "keyword",
                        "ignore_above": 256
                      }
                    }
                  }
                }
              },
              {
                "objects": {
                  "match_mapping_type": "object",
                  "mapping": {
                    "type": "nested"
                  }
                }
              }
            ],
            "dynamic": "true",
            "properties": {
              "stationGlobalNumber": {
                "type": "keyword"
              },
              "pointStationCode": {
                "type": "keyword"
              },
              "siteId": {
                "type": "keyword"
              },
              "stationNumber": {
                "type": "keyword"
              },
              "content": {
                "type": "nested"
              }
            }
          }
        }
        ]]>
    </property>
    <property name="searchDataByFields">
        <![CDATA[{
          #if($nestedQuery)
          "query": {
            "bool": {
              "must": [
                  {"nested": {
                    "path": "$nestedPath",
                    "query": {
                      "bool": {
                        "must": [
                          $nestedQuery
                        ]
                      }
                    }
                  }}
              ]
            }
          },
          #end
          #foreach($groupField in $groupFields)
            #set($groupFieldName = "group_by_nested_$groupField")
            #set($fieldWithKeyword = "${groupField}.keyword")
            #if($velocityCount == 0)
              "aggs": {
                "nested_path": {
                  "nested": {
                    "path": "content"
                  },
            #end
                "aggs": {
                    "$groupFieldName": {
                      "terms": {
                        "field": "$fieldWithKeyword",
                        "size": 100
                      }
            #if($velocityCount < $groupFields.size() - 1) , #end
          #end
          #foreach($groupField in $groupFields)
            }}
            #if($velocityCount == $groupFields.size() - 1) }}, #end
          #end
          "from": #[from],
          "size": #[size],
          "sort": [
            {
              "stationNumber": {
                "order": "asc",
                "mode": "max"
              }
            }
          ]
        }]]>
    </property>
</properties>
