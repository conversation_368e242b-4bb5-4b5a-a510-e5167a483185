<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dhcc.dsp.business.dao.datasupplement.ExcelAuthMapper">
    <resultMap id="BaseResultMap" type="com.dhcc.dsp.business.model.datasupplement.ExcelAuth">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="CORRELATIONID" jdbcType="VARCHAR" property="correlationId"/>
        <result column="USERID" jdbcType="VARCHAR" property="userId"/>
        <result column="OPERATE" jdbcType="NUMERIC" property="operate"/>
        <result column="ACCESSTYPE" jdbcType="NUMERIC" property="accessType"/>
        <result column="TYPE" jdbcType="NUMERIC" property="type"/>
    </resultMap>
    <sql id="Base_Column_List">
    ID, CORRELATIONID, USERID, OPERATE, ACCESSTYPE, TYPE
    </sql>

    <select id="selectByCorrelationId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from EXCEL_AUTH
        <where>
            <if test="correlationId != null and correlationId !=''">
                and CORRELATIONID = #{correlationId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <insert id="insert" parameterType="com.dhcc.dsp.business.model.datasupplement.ExcelAuth">
        insert into EXCEL_AUTH (ID, CORRELATIONID, USERID, OPERATE, ACCESSTYPE, TYPE)
        values (#{id,jdbcType=VARCHAR}, #{correlationId,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR},
        #{operate,jdbcType=NUMERIC},#{accessType,jdbcType=NUMERIC},#{type,jdbcType=NUMERIC})
    </insert>

    <delete id="deleteById" parameterType="java.lang.String">
        delete from EXCEL_AUTH
        where ID = #{id,jdbcType=VARCHAR}
    </delete>

    <select id="selectOneById" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from EXCEL_AUTH
        where ID = #{id,jdbcType=VARCHAR}
    </select>

    <select id="selectOneByCorrelationIdAndUserId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from EXCEL_AUTH
        <where>
            <if test="correlationId != null and correlationId !=''">
                and CORRELATIONID = #{correlationId,jdbcType=VARCHAR}
            </if>
            <if test="userId != null and userId !=''">
                and USERID = #{userId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>


</mapper>