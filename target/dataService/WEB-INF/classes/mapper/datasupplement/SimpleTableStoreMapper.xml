<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dhcc.dsp.business.dao.datasupplement.SimpleTableStoreMapper">
    <resultMap id="BaseResultMap" type="com.dhcc.dsp.business.model.datasupplement.SimpleTableStore">
        <id column="TABLE_ID" jdbcType="VARCHAR" property="tableId"/>
        <result column="SITE_ID" jdbcType="VARCHAR" property="siteId"/>
        <result column="MACHINE_SET" jdbcType="VARCHAR" property="machineSet"/>
        <result column="TYPE" jdbcType="VARCHAR" property="type"/>
        <result column="THEAD" jdbcType="VARCHAR" property="thead"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="RECORDTIME" jdbcType="VARCHAR" property="recordTime"/>
        <result column="CREATEBY" jdbcType="VARCHAR" property="createBy"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs"
               type="com.dhcc.dsp.business.model.datasupplement.SimpleTableStore">
        <result column="TBODY" jdbcType="CLOB" property="tbody"/>
    </resultMap>
    <sql id="Base_Column_List">
    TABLE_ID, MACHINE_SET, TYPE, THEAD, TBODY, RECORDTIME, CREATEBY
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        from SIMPLE_TABLE_STORE
        where TABLE_ID = #{tableId,jdbcType=VARCHAR}
    </select>

    <select id="queryTableList" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from SIMPLE_TABLE_STORE
        <where>
            <if test="siteId != null and siteId !=''">
                and SITE_ID = #{siteId,jdbcType=VARCHAR}
            </if>
            <if test="machineSet != null and machineSet !=''">
                and MACHINE_SET = #{machineSet,jdbcType=VARCHAR}
            </if>
            <if test="type != null and type !=''">
                and TYPE = #{type,jdbcType=VARCHAR}
            </if>
        </where>
        order by RECORDTIME desc
    </select>


    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from SIMPLE_TABLE_STORE
        where TABLE_ID = #{tableId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.dhcc.dsp.business.model.datasupplement.SimpleTableStore">
        insert into SIMPLE_TABLE_STORE (TABLE_ID, SITE_ID, MACHINE_SET,
        TYPE, THEAD, TBODY,STATUS,RECORDTIME,CREATEBY)
        values (#{tableId,jdbcType=VARCHAR}, #{siteId,jdbcType=VARCHAR}, #{machineSet,jdbcType=VARCHAR},
        #{type,jdbcType=VARCHAR}, #{thead,jdbcType=VARCHAR}, #{tbody,jdbcType=CLOB}, #{status,jdbcType=VARCHAR},
        #{recordTime,jdbcType=VARCHAR},#{createBy,jdbcType=VARCHAR})
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.dhcc.dsp.business.model.datasupplement.SimpleTableStore">
        update SIMPLE_TABLE_STORE
            set SITE_ID = #{siteId,jdbcType=VARCHAR},
            MACHINE_SET = #{machineSet,jdbcType=VARCHAR},
            TYPE = #{type,jdbcType=VARCHAR},
            THEAD = #{thead,jdbcType=VARCHAR},
            TBODY = #{tbody,jdbcType=VARCHAR},
            RECORDTIME = #{recordTime,jdbcType=VARCHAR}
        where TABLE_ID = #{tableId,jdbcType=VARCHAR}
    </update>

    <select id="queryTemplateList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from SIMPLE_TABLE_STORE
        <where>
            <if test="machineSet != null and machineSet !=''">
                and MACHINE_SET like concat ('%',concat(#{machineSet},'%'))
            </if>
            <if test="type != null and type !=''">
                and TYPE = #{type,jdbcType=VARCHAR}
            </if>
        </where>
        order by RECORDTIME desc limit #{startNum},#{pageSize}
    </select>

    <select id="selTemplateList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from SIMPLE_TABLE_STORE
        <where>
            <if test="machineSet != null and machineSet !=''">
                and MACHINE_SET like concat ('%',concat(#{machineSet},'%'))
            </if>
            <if test="type != null and type !=''">
                and TYPE = #{type,jdbcType=VARCHAR}
            </if>
        </where>
        order by RECORDTIME desc
    </select>

    <select id="queryTemplateCount" parameterType="java.lang.String" resultType="java.lang.Integer">
        select
        COUNT(*)
        from SIMPLE_TABLE_STORE
        <where>
            <if test="_parameter != null and _parameter !=''">
                and TYPE = #{type,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

</mapper>